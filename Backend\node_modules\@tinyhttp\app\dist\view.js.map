{"version": 3, "file": "view.js", "sourceRoot": "", "sources": ["../src/view.ts"], "names": [], "mappings": "AAAA;;;;;;;GAOG;;;;;;;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAA;AAClC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,WAAW,CAAA;AAGrE,SAAS,OAAO,CAAC,IAAY;IAC3B,IAAI,CAAC;QACH,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA;IACvB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,SAAS,CAAA;IAClB,CAAC;AACH,CAAC;AAED;;;;;;;;;;;;GAYG;AAEH,MAAM,OAAO,IAAI;IAOf,YACE,IAAY,EACZ,OAIK,EAAE;;;QAPT,SAAI,GAAsB,EAAE,CAAA;QAS1B,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,IAAI,CAAC,IAAI;YAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACpC,IAAI,IAAI,CAAC,aAAa;YAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;QAE/D,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa;YAClC,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAA;QAEnF,IAAI,QAAQ,GAAG,IAAI,CAAA;QAEnB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACd,yCAAyC;YACzC,IAAI,CAAC,GAAG,GAAG,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAG,CAAC,CAAC,MAAK,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAA;YAE1F,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAA;QACtB,CAAC;QAED,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,OAAO,0CAAG,IAAI,CAAC,GAAG,CAAC,CAAA;YAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;QACrF,MAAM,IAAI,GAAG,uBAAA,IAAI,qCAAQ,MAAZ,IAAI,EAAS,QAAQ,CAAC,CAAA;QACnC,MAAM,IAAI,GACR,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;YAC9C,CAAC,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG;YAChG,CAAC,CAAC,cAAc,IAAI,CAAC,IAAI,GAAG,CAAA;QAChC,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,cAAc,IAAI,EAAE,CAAC,CAAA;QAC9E,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACpC,IAAI,CAAC,IAAI,GAAG,IAAc,CAAA;IAC5B,CAAC;IAqCD,MAAM,CAAC,OAAsB,EAAE,IAA6B,EAAE,EAA8C;QAC1G,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA;IAC3C,CAAC;CACF;sEAvCS,IAAY;IAClB,IAAI,IAAwB,CAAA;IAC5B,MAAM,KAAK,GAAI,EAAe,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/C,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QACrB,mBAAmB;QACnB,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC/B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;QACxB,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAA;QAE1B,mBAAmB;QACnB,IAAI,GAAG,uBAAA,IAAI,sCAAS,MAAb,IAAI,EAAU,GAAG,EAAE,IAAI,CAAC,CAAA;IACjC,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC,yCACQ,GAAW,EAAE,IAAY;IAChC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;IAEpB,eAAe;IACf,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;IAC1B,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAExB,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,EAAE,EAAE,CAAC;QACnB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,qBAAqB;IACrB,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAA;IACpD,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAEpB,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,EAAE,EAAE,CAAC;QACnB,OAAO,IAAI,CAAA;IACb,CAAC;AACH,CAAC"}