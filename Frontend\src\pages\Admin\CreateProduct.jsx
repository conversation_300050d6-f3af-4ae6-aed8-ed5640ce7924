import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { nanoid } from "@reduxjs/toolkit";
import { addProduct } from "../../store/Actions/ProductAction";

const CreateProduct = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      category: "men's clothing",
    },
  });
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const AddProductHandler = (data) => {
    data.id = nanoid();
    dispatch(addProduct(data)); //dispatching the product data to the store.
    reset();
    navigate("/products");
  };
  return (
    <>
      Add Product Page
      <br />
      <form onSubmit={handleSubmit(AddProductHandler)}>
        <input
          {...register("image", { required: "This field is required." })}
          type="url"
          name="image"
          placeholder="Enter the image url"
        />
        {errors.image && <p>{errors.image.message}</p>}
        <br />
        <input
          {...register("title", { required: "This field is required." })}
          type="text"
          name="title"
          placeholder="Enter the title"
        />
        {errors.title && <p>{errors.title.message}</p>}
        <br />
        <input
          {...register("price", { required: "This field is required." })}
          type="number"
          name="price"
          placeholder="Enter the price"
        />
        {errors.price && <p>{errors.price.message}</p>}
        <br />
        <textarea
          {...register("description", { required: "This field is required." })}
          name="description"
          placeholder="Enter the description"
        >
        </textarea>
        {errors.description && <p>{errors.description.message}</p>}
        <br />
        <label htmlFor="category">Select the category</label>
        <select
          {...register("category", { required: "This field is required." })}
          className="border border-gray-300 rounded-md p-2"
        >
          <option className="bg-gray-700 text-white" value="men's clothing">
            Men's Clothing
          </option>
          <option className="bg-gray-700 text-white" value="women's clothing">
            Women's Clothing
          </option>
          <option className="bg-gray-700 text-white" value="jewelery">
            Jewelery
          </option>
          <option className="bg-gray-700 text-white" value="electronics">
            Electronics
          </option>
        </select>
        <br />
        <button
          className="bg-blue-500 text-white px-4 py-2 rounded"
          type="submit"
        >
          Add the Product
        </button>
      </form>
    </>
  );
};

export default CreateProduct;
