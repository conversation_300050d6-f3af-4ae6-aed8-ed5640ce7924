{"name": "@tinyhttp/app", "version": "2.5.2", "description": "0-legacy, tiny & fast web framework as a replacement of Express", "type": "module", "homepage": "https://tinyhttp.v1rtl.site", "repository": {"type": "git", "url": "https://github.com/tinyhttp/tinyhttp.git", "directory": "packages/app"}, "funding": {"type": "individual", "url": "https://github.com/tinyhttp/tinyhttp?sponsor=1"}, "types": "./dist/index.d.ts", "exports": "./dist/index.js", "files": ["dist"], "engines": {"node": ">=14.21.3"}, "keywords": ["tinyhttp", "router", "backend", "http", "framework", "api"], "author": "v1rtl", "license": "MIT", "dependencies": {"header-range-parser": "1.1.3", "regexparam": "^2.0.2", "@tinyhttp/cookie": "2.1.1", "@tinyhttp/proxy-addr": "2.2.1", "@tinyhttp/req": "2.2.5", "@tinyhttp/res": "2.2.5", "@tinyhttp/router": "2.2.3"}, "scripts": {"build": "tsc"}}