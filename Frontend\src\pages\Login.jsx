import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { loginUser } from "../store/Actions/UserAction";

const Login = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const loginHandler = (data) => {
    dispatch(loginUser(data));     //dispatching the user data to the store
    reset();
    navigate("/products");
  };
  return (
    <>Login Page<br/>
    <form onSubmit={handleSubmit(loginHandler)}>
      <input
        {...register("email", { required: "This field is required." })}
        type="email"
        name="email"
        placeholder="Enter the email"
      />
      {errors.email && <p>{errors.email.message}</p>}
      <br />
      <input
        {...register("password", { required: "This field is required." })}
        type="password"
        name="password"
        placeholder="Enter the password"
      />
      {errors.password && <p>{errors.password.message}</p>}
      <br />
      <button className="bg-blue-500 text-white px-4 py-2 rounded" type="submit">Login</button>
      
    </form>
    <p>Don't have an account? <Link className="text-blue-500" to="/register">Register</Link></p>
    </>
  );
};

export default Login;
