import React from 'react'
import { useSelector } from 'react-redux'
import ProductCard from '../components/ProductCard'

const Products = () => {
  const products = useSelector((state) => state.products.product);
  console.log(products);
  return (
    <div className='flex flex-wrap gap-5 m-2'>
      {products.map((product) => {
    return <ProductCard key={product.id} product={product} />
  })}
    </div>
  )
}

export default Products
