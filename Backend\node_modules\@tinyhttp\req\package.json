{"name": "@tinyhttp/req", "version": "2.2.5", "type": "module", "description": "request extensions for tinyhttp", "homepage": "https://tinyhttp.v1rtl.site", "repository": {"type": "git", "url": "https://github.com/tinyhttp/tinyhttp.git", "directory": "packages/req"}, "types": "./dist/index.d.ts", "exports": "./dist/index.js", "keywords": ["tinyhttp", "node.js", "web framework", "web", "backend", "req", "request"], "engines": {"node": ">=12.20.0"}, "author": "v1rtl", "license": "MIT", "dependencies": {"header-range-parser": "^1.1.3", "@tinyhttp/accepts": "2.2.3", "@tinyhttp/type-is": "2.2.4", "@tinyhttp/url": "2.1.1"}, "scripts": {"build": "tsc"}}