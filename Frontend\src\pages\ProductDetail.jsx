import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { deleteProduct, updateProduct } from "../store/Actions/ProductAction";

const ProductDetail = () => {
  const { id } = useParams();
  const products = useSelector((state) => state.products.product);
  const user = useSelector((state) => state.user.user);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const product = products.find((product) => product.id === id);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: product,
  });

  const updateProductHandler = (data) => {
    dispatch(updateProduct(data));
  };

  const deleteHandler = () => {
    dispatch(deleteProduct(id));
    navigate("/products");

  };

  return (
    <>
      <div className="w-full flex gap-5 p-2 border border-gray-300 rounded-md">
        <img
          className="w-1/2 h-48 object-cover mr-3"
          src={product.image}
          alt={product.title}
        />
        <div className="w-2/3">
          <h2 className="text-2xl font-bold">{product.title}</h2>
          <p className="text-gray-500">{product.category}</p>
          <p className="text-gray-500 text-lg">{product.description}</p>
          <p className="text-2xl font-bold">{product.price}</p>
          <button className="bg-blue-500 text-white px-4 py-2 rounded mr-3 cursor-pointer">
            Add to Cart
          </button>
          {user?.isAdmin && (
            <button type="button" className="bg-red-500 text-white px-4 py-2 rounded cursor-pointer" onClick={deleteHandler}>
              Delete Product
            </button>
          )}
        </div>
      </div>
      {user?.isAdmin && (
        <div className="w-full flex gap-5 p-2 border border-gray-300 rounded-md">
          <h2 className="text-2xl font-bold">Update Product</h2>
          <form onSubmit={handleSubmit(updateProductHandler)}>
            <input
              {...register("title", { required: "This field is required." })}
              type="text"
              name="title"
              id="title"
              className="border border-gray-300 rounded-md p-2 m-2"
            />
            {errors.title && <p>{errors.title.message}</p>}
            <br />
            <textarea
              {...register("description", {
                required: "This field is required.",
              })}
              type="text"
              name="description"
              id="description"
              className="border border-gray-300 rounded-md p-2 m-2"
            ></textarea>
            {errors.description && <p>{errors.description.message}</p>}
            <br />
            <input
              {...register("price", { required: "This field is required." })}
              type="text"
              name="price"
              id="price"
              className="border border-gray-300 rounded-md p-2 m-2"
            />
            {errors.price && <p>{errors.price.message}</p>}
            <br />
            <label htmlFor="category">Category</label>
            <select
              {...register("category", { required: "This field is required." })}
              name="category"
              id="category"
              className="border border-gray-300 rounded-md p-2 m-2"
            >
              <option className="bg-gray-700 text-white" value="men's clothing">
                Men's Clothing
              </option>
              <option
                className="bg-gray-700 text-white"
                value="women's clothing"
              >
                Women's Clothing
              </option>
              <option className="bg-gray-700 text-white" value="jewelery">
                Jewelery
              </option>
              <option className="bg-gray-700 text-white" value="electronics">
                Electronics
              </option>
            </select>
            {errors.category && <p>{errors.category.message}</p>}
            <br />
            <label htmlFor="image">Image</label>
            <input
              {...register("image", { required: "This field is required." })}
              type="text"
              name="image"
              id="image"
              className="border border-gray-300 rounded-md p-2 m-2"
            />{" "}
            {errors.image && <p>{errors.image.message}</p>}
            <br />
            <button className="bg-blue-500 text-white px-4 py-2 rounded cursor-pointer">
              Update Product
            </button>
          </form>
        </div>
      )}
    </>
  );
};

export default ProductDetail;
