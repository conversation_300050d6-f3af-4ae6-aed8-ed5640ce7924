import React from "react";
import { Link } from "react-router-dom";

const ProductCard = ({product}) => {
  return (
    <div className='w-1/4 border border-gray-300 rounded-md p-2 m-2'>
      <img className="w-full h-48 object-cover" src={product.image} alt={product.title} />
      <h2 className="text-xl font-bold">{product.title}</h2>
      <p className="text-gray-500">{product.description}</p>
      <p className="text-xl font-bold">{product.price}</p>
      <Link to={`/product/${product.id}`} className="bg-blue-500 text-white px-4 py-2 rounded cursor-pointer">More Details</Link>
    </div>
  )
}

export default ProductCard
