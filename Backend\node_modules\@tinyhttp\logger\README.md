<div align="center">

# @tinyhttp/logger

[![npm][npm-img]][npm-url] [![GitHub Workflow Status][gh-actions-img]][github-actions] [![Coverage][cov-img]][cov-url]

</div>

Minimal and flexible HTTP logger

## Install

```sh
pnpm i @tinyhttp/logger
```

## API

```ts
import { logger } from '@tinyhttp/logger'
```

### `logger(options)`

Returns the middleware for logging HTTP requests.

#### Options

- `methods`: a list of HTTP methods to log. Defaults to `http`'s `METHODS`.
- `timestamp.format`: timestamp format. It is consumed by the [dayjs](https://day.js.org) library. If a string is specified, it is used as a format; otherwise just enabled.
- `output.callback`: a function that receives the log generated by the logger.
- `output.color`: a property that determines whether the logger will generate a message with color. Useful for logging into the console; disable if logging into a file or other colorless environments.
- `emoji`: enable emojis for HTTP status codes. See [http-status-emojis](https://github.com/bendrucker/http-status-emojis/blob/master/index.js) for a full list.
- `ip`: log IP address.

## Example

```ts
import { App } from '@tinyhttp/app'
import { logger } from '@tinyhttp/logger'

new App()
  .use(
    logger({
      methods: ['GET', 'POST'],
      timestamp: { format: 'HH:mm:ss' },
      output: { callback: console.log, color: false }
    })
  )
  .get('/', (req, res) => res.send('Hello world'))
  .post('/', (req, res) => res.send('Sent POST'))
  .listen(3000)
```

To Log a level, use the enum `LogLevel`

```ts
import { App } from '@tinyhttp/app'
import { logger, LogLevel } from '@tinyhttp/logger'

new App()
  .use(
    logger({
      methods: ['GET', 'POST'],
      timestamp: { format: 'HH:mm:ss' },
      output: { callback: console.log, color: false, level: LogLevel.warn }
    })
  )
  .get('/', (req, res) => res.send('Hello world'))
  .listen(3000)
```

This also includes a simple file logger. To stream to a file, simply supply the filename in the options. Supported file names innclude
`./file.log` or `./log/tiny.log`

```ts
import { App } from '@tinyhttp/app'
import { logger } from '@tinyhttp/logger'

new App()
  .use(
    logger({
      methods: ['GET', 'POST'],
      timestamp: { format: 'HH:mm:ss' },
      output: { callback: console.log, color: false, filename: './log/tiny.log' }
    })
  )
  .get('/', (req, res) => res.send('Hello world'))
  .listen(3000)
```

## Alternatives

- [Pino HTTP](https://github.com/pinojs/pino-http) - high-speed HTTP logger for Node.js
- [chrona](https://github.com/xambassador/chrona) - Simple HTTP request logger middleware for express.js inspired from koa-logger, written in typescript.

[npm-url]: https://npmjs.com/package/@tinyhttp/logger
[github-actions]: https://github.com/tinyhttp/logger/actions
[gh-actions-img]: https://img.shields.io/github/actions/workflow/status/tinyhttp/logger/ci.yml?branch=master&style=for-the-badge&color=hotpink&label=&logo=github
[cov-img]: https://img.shields.io/coveralls/github/tinyhttp/logger?style=for-the-badge&color=hotpink
[cov-url]: https://coveralls.io/github/tinyhttp/logger
[npm-img]: https://img.shields.io/npm/dt/@tinyhttp/logger?style=for-the-badge&color=hotpink
