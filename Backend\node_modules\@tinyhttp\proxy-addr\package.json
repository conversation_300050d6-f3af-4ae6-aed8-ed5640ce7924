{"name": "@tinyhttp/proxy-addr", "version": "2.2.1", "type": "module", "description": "proxy-addr rewrite with TypeScript and ESM support", "homepage": "https://tinyhttp.v1rtl.site", "repository": {"type": "git", "url": "https://github.com/tinyhttp/tinyhttp.git", "directory": "packages/proxy-addr"}, "types": "./dist/index.d.ts", "exports": "./dist/index.js", "keywords": ["tinyhttp", "node.js", "web framework", "web", "backend", "proxy-addr", "ip", "net", "network"], "engines": {"node": ">=12.20.0"}, "author": "v1rtl", "license": "MIT", "dependencies": {"ipaddr.js": "^2.2.0", "@tinyhttp/forwarded": "2.1.2"}, "scripts": {"build": "tsc"}}