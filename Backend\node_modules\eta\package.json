{"name": "eta", "version": "3.5.0", "description": "Lightweight, fast, and powerful embedded JS template engine", "keywords": ["handlebars", "ejs", "eta", "template engine", "embedded template engine", "layouts", "partials", "typescript types"], "homepage": "https://eta.js.org", "type": "commonjs", "main": "./dist/eta.umd.js", "umd:main": "./dist/eta.umd.js", "unpkg": "./dist/browser.umd.js", "module": "./dist/eta.module.mjs", "types": "./dist/types/index.d.ts", "source": "src/index.ts", "exports": {"types": "./dist/types/index.d.ts", "browser": "./dist/browser.umd.js", "import": "./dist/eta.module.mjs", "require": "./dist/eta.umd.js", "default": "./dist/eta.umd.js"}, "sideEffects": false, "files": ["dist"], "author": "<PERSON> <<EMAIL>>", "funding": "https://github.com/eta-dev/eta?sponsor=1", "repository": {"type": "git", "url": "git+https://github.com/eta-dev/eta.git"}, "bugs": {"url": "https://github.com/eta-dev/eta/issues"}, "license": "MIT", "engines": {"node": ">=6.0.0"}, "scripts": {"build:node": "microbundle src/index.ts --target node --format esm && microbundle src/index.ts --target node --format umd", "build:browser": "microbundle src/browser.ts --target web --format umd,modern --output dist/browser.js", "build": "npm run build:node && npm run build:browser", "commit": "git-cz", "format": "prettier --write '{src,test}/**/**.ts'", "lint": "eslint src/*.ts test/*.spec.ts --ext .js,.ts", "prebuild": "<PERSON><PERSON><PERSON> dist", "release": "npm run build && np && npx jsr publish", "postversion": "node ./jsr.release.js", "size": "size-limit", "start": "microbundle watch", "test": "jest --coverage && npm run size", "test:prod": "npm run lint && npm run test -- --no-cache", "test:watch": "jest --coverage --watch"}, "size-limit": [{"path": "dist/browser.umd.js", "limit": "3.5 KB"}], "lint-staged": {"{src,test}/**/**.ts": ["eslint"]}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "jest": {"transform": {".(ts)": "ts-jest"}, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "testEnvironment": "node", "testRegex": "\\/test\\/.*(\\.spec\\.ts)$", "moduleFileExtensions": ["ts", "js"], "coveragePathIgnorePatterns": ["/node_modules/", "/test/"], "coverageThreshold": {"global": {"branches": 85, "functions": 95, "lines": 95, "statements": 95}}, "collectCoverageFrom": ["src/!(browser).ts"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@size-limit/preset-small-lib": "^8.2.4", "@types/jest": "^29.4.0", "@types/node": "^18.14.2", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "commitizen": "^4.3.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.35.0", "eslint-config-prettier": "^8.6.0", "husky": "^4.3.8", "jest": "^29.4.3", "lint-staged": "^13.1.2", "microbundle": "^0.15.1", "np": "^7.6.3", "prettier": "^2.8.4", "rimraf": "^4.1.2", "shelljs": "^0.8.5", "size-limit": "^8.2.4", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "tslib": "^2.5.0", "typescript": "^5.1.3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}