{"name": "Backend", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/@polka/url": {"version": "1.0.0-next.29", "resolved": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.29.tgz", "integrity": "sha512-wwQAWhWSuHaag8c4q/KN/vCoeOJYshAIvMQwD4GpSb3OiZklFfvAgmj0VCBBImRpuF/aFgIRzllXlVX93Jevww==", "license": "MIT"}, "node_modules/@tinyhttp/accepts": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/@tinyhttp/accepts/-/accepts-2.2.3.tgz", "integrity": "sha512-9pQN6pJAJOU3McmdJWTcyq7LLFW8Lj5q+DadyKcvp+sxMkEpktKX5sbfJgJuOvjk6+1xWl7pe0YL1US1vaO/1w==", "license": "MIT", "dependencies": {"mime": "4.0.4", "negotiator": "^0.6.3"}, "engines": {"node": ">=12.20.0"}, "funding": {"type": "individual", "url": "https://github.com/tinyhttp/tinyhttp?sponsor=1"}}, "node_modules/@tinyhttp/app": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/@tinyhttp/app/-/app-2.5.2.tgz", "integrity": "sha512-DcB3Y8GQppLQlO2VxRYF7LzTEAoZb+VRQXuIsErcu2fNaM1xdx6NQZDso5rlZUiaeg6KYYRfU34N4XkZbv6jSA==", "license": "MIT", "dependencies": {"@tinyhttp/cookie": "2.1.1", "@tinyhttp/proxy-addr": "2.2.1", "@tinyhttp/req": "2.2.5", "@tinyhttp/res": "2.2.5", "@tinyhttp/router": "2.2.3", "header-range-parser": "1.1.3", "regexparam": "^2.0.2"}, "engines": {"node": ">=14.21.3"}, "funding": {"type": "individual", "url": "https://github.com/tinyhttp/tinyhttp?sponsor=1"}}, "node_modules/@tinyhttp/content-disposition": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/@tinyhttp/content-disposition/-/content-disposition-2.2.2.tgz", "integrity": "sha512-crXw1txzrS36huQOyQGYFvhTeLeG0Si1xu+/l6kXUVYpE0TjFjEZRqTbuadQLfKGZ0jaI+jJoRyqaWwxOSHW2g==", "license": "MIT", "engines": {"node": ">=12.20.0"}, "funding": {"type": "individual", "url": "https://github.com/tinyhttp/tinyhttp?sponsor=1"}}, "node_modules/@tinyhttp/content-type": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/@tinyhttp/content-type/-/content-type-0.1.4.tgz", "integrity": "sha512-dl6f3SHIJPYbhsW1oXdrqOmLSQF/Ctlv3JnNfXAE22kIP7FosqJHxkz/qj2gv465prG8ODKH5KEyhBkvwrueKQ==", "license": "MIT", "engines": {"node": ">=12.4"}}, "node_modules/@tinyhttp/cookie": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/@tinyhttp/cookie/-/cookie-2.1.1.tgz", "integrity": "sha512-h/kL9jY0e0Dvad+/QU3efKZww0aTvZJslaHj3JTPmIPC9Oan9+kYqmh3M6L5JUQRuTJYFK2nzgL2iJtH2S+6dA==", "license": "MIT", "engines": {"node": ">=12.20.0"}, "funding": {"type": "individual", "url": "https://github.com/tinyhttp/tinyhttp?sponsor=1"}}, "node_modules/@tinyhttp/cookie-signature": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/@tinyhttp/cookie-signature/-/cookie-signature-2.1.1.tgz", "integrity": "sha512-VDsSMY5OJfQJIAtUgeQYhqMPSZptehFSfvEEtxr+4nldPA8IImlp3QVcOVuK985g4AFR4Hl1sCbWCXoqBnVWnw==", "license": "MIT", "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/cors": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@tinyhttp/cors/-/cors-2.0.1.tgz", "integrity": "sha512-qrmo6WJuaiCzKWagv2yA/kw6hIISfF/hOqPWwmI6w0o8apeTMmRN3DoCFvQ/wNVuWVdU5J4KU7OX8aaSOEq51A==", "license": "MIT", "dependencies": {"@tinyhttp/vary": "^0.1.3"}, "engines": {"node": ">=12.20 || 14.x || >=16"}}, "node_modules/@tinyhttp/encode-url": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/@tinyhttp/encode-url/-/encode-url-2.1.1.tgz", "integrity": "sha512-AhY+JqdZ56qV77tzrBm0qThXORbsVjs/IOPgGCS7x/wWnsa/Bx30zDUU/jPAUcSzNOzt860x9fhdGpzdqbUeUw==", "license": "MIT", "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/etag": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/@tinyhttp/etag/-/etag-2.1.2.tgz", "integrity": "sha512-j80fPKimGqdmMh6962y+BtQsnYPVCzZfJw0HXjyH70VaJBHLKGF+iYhcKqzI3yef6QBNa8DKIPsbEYpuwApXTw==", "license": "MIT", "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/forwarded": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/@tinyhttp/forwarded/-/forwarded-2.1.2.tgz", "integrity": "sha512-9H/eulJ68ElY/+zYpTpNhZ7vxGV+cnwaR6+oQSm7bVgZMyuQfgROW/qvZuhmgDTIxnGMXst+Ba4ij6w6Krcs3w==", "license": "MIT", "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/logger": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@tinyhttp/logger/-/logger-2.1.0.tgz", "integrity": "sha512-Ma1fJ9CwUbn9r61/4HW6+nflsVoslpOnCrfQ6UeZq7GGIgwLzofms3HoSVG7M+AyRMJpxlfcDdbH5oFVroDMKA==", "license": "MIT", "dependencies": {"colorette": "^2.0.20", "dayjs": "^1.11.13", "http-status-emojis": "^2.2.0"}, "engines": {"node": ">=14.18 || >=16.20"}}, "node_modules/@tinyhttp/proxy-addr": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/@tinyhttp/proxy-addr/-/proxy-addr-2.2.1.tgz", "integrity": "sha512-BicqMqVI91hHq2BQmnqJUh0FQUnx7DncwSGgu2ghlh+JZG2rHK2ZN/rXkfhrx1rrUw6hnd0L36O8GPMh01+dDQ==", "license": "MIT", "dependencies": {"@tinyhttp/forwarded": "2.1.2", "ipaddr.js": "^2.2.0"}, "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/req": {"version": "2.2.5", "resolved": "https://registry.npmjs.org/@tinyhttp/req/-/req-2.2.5.tgz", "integrity": "sha512-trfsXwtmsNjMcGKcLJ+45h912kLRqBQCQD06ams3Tq0kf4gHLxjHjoYOC1Z9yGjOn81XllRx8wqvnvr+Kbe3gw==", "license": "MIT", "dependencies": {"@tinyhttp/accepts": "2.2.3", "@tinyhttp/type-is": "2.2.4", "@tinyhttp/url": "2.1.1", "header-range-parser": "^1.1.3"}, "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/res": {"version": "2.2.5", "resolved": "https://registry.npmjs.org/@tinyhttp/res/-/res-2.2.5.tgz", "integrity": "sha512-yBsqjWygpuKAVz4moWlP4hqzwiDDqfrn2mA0wviJAcgvGiyOErtlQwXY7aj3aPiCpURvxvEFO//Gdy6yV+xEpA==", "license": "MIT", "dependencies": {"@tinyhttp/content-disposition": "2.2.2", "@tinyhttp/cookie": "2.1.1", "@tinyhttp/cookie-signature": "2.1.1", "@tinyhttp/encode-url": "2.1.1", "@tinyhttp/req": "2.2.5", "@tinyhttp/send": "2.2.3", "@tinyhttp/vary": "^0.1.3", "es-escape-html": "^0.1.1", "mime": "4.0.4"}, "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/router": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/@tinyhttp/router/-/router-2.2.3.tgz", "integrity": "sha512-O0MQqWV3Vpg/uXsMYg19XsIgOhwjyhTYWh51Qng7bxqXixxx2PEvZWnFjP7c84K7kU/nUX41KpkEBTLnznk9/Q==", "license": "MIT", "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/send": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/@tinyhttp/send/-/send-2.2.3.tgz", "integrity": "sha512-o4cVHHGQ8WjVBS8UT0EE/2WnjoybrfXikHwsRoNlG1pfrC/Sd01u1N4Te8cOd/9aNGLr4mGxWb5qTm2RRtEi7g==", "license": "MIT", "dependencies": {"@tinyhttp/content-type": "^0.1.4", "@tinyhttp/etag": "2.1.2", "mime": "4.0.4"}, "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/type-is": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/@tinyhttp/type-is/-/type-is-2.2.4.tgz", "integrity": "sha512-7F328NheridwjIfefBB2j1PEcKKABpADgv7aCJaE8x8EON77ZFrAkI3Rir7pGjopV7V9MBmW88xUQigBEX2rmQ==", "license": "MIT", "dependencies": {"@tinyhttp/content-type": "^0.1.4", "mime": "4.0.4"}, "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/url": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/@tinyhttp/url/-/url-2.1.1.tgz", "integrity": "sha512-POJeq2GQ5jI7Zrdmj22JqOijB5/GeX+LEX7DUdml1hUnGbJOTWDx7zf2b5cCERj7RoXL67zTgyzVblBJC+NJWg==", "license": "MIT", "engines": {"node": ">=12.20.0"}}, "node_modules/@tinyhttp/vary": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/@tinyhttp/vary/-/vary-0.1.3.tgz", "integrity": "sha512-SoL83sQXAGiHN1jm2VwLUWQSQeDAAl1ywOm6T0b0Cg1CZhVsjoiZadmjhxF6FHCCY7OHHVaLnTgSMxTPIDLxMg==", "license": "MIT", "engines": {"node": ">=12.20"}}, "node_modules/chalk": {"version": "5.4.1", "resolved": "https://registry.npmjs.org/chalk/-/chalk-5.4.1.tgz", "integrity": "sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==", "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chokidar": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-4.0.3.tgz", "integrity": "sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==", "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/colorette": {"version": "2.0.20", "resolved": "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz", "integrity": "sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==", "license": "MIT"}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "https://registry.npmjs.org/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==", "license": "MIT"}, "node_modules/dot-prop": {"version": "9.0.0", "resolved": "https://registry.npmjs.org/dot-prop/-/dot-prop-9.0.0.tgz", "integrity": "sha512-1gxPBJpI/pcjQhKgIU91II6Wkay+dLcN3M6rf2uwP8hRur3HtQXjVrdAK3sjC0piaEuxzMwjXChcETiJl47lAQ==", "license": "MIT", "dependencies": {"type-fest": "^4.18.2"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/es-escape-html": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/es-escape-html/-/es-escape-html-0.1.1.tgz", "integrity": "sha512-yUx1o+8RsG7UlszmYPtks+dm6Lho2m8lgHMOsLJQsFI0R8XwUJwiMhM1M4E/S8QLeGyf6MkDV/pWgjQ0tdTSyQ==", "license": "MIT", "engines": {"node": ">=12.x"}}, "node_modules/eta": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/eta/-/eta-3.5.0.tgz", "integrity": "sha512-e3x3FBvGzeCIHhF+zhK8FZA2vC5uFn6b4HJjegUbIWrDb4mJ7JjTGMJY9VGIbRVpmSwHopNiaJibhjIr+HfLug==", "license": "MIT", "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://github.com/eta-dev/eta?sponsor=1"}}, "node_modules/header-range-parser": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/header-range-parser/-/header-range-parser-1.1.3.tgz", "integrity": "sha512-B9zCFt3jH8g09LR1vHL4pcAn8yMEtlSlOUdQemzHMRKMImNIhhszdeosYFfNW0WXKQtXIlWB+O4owHJKvEJYaA==", "license": "MIT", "engines": {"node": ">=12.22.0"}}, "node_modules/http-status-emojis": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/http-status-emojis/-/http-status-emojis-2.2.0.tgz", "integrity": "sha512-ompKtgwpx8ff0hsbpIB7oE4ax1LXoHmftsHHStMELX56ivG3GhofTX8ZHWlUaFKfGjcGjw6G3rPk7dJRXMmbbg==", "license": "MIT"}, "node_modules/inflection": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/inflection/-/inflection-3.0.2.tgz", "integrity": "sha512-+Bg3+kg+J6JUWn8J6bzFmOWkTQ6L/NHfDRSYU+EVvuKHDxUDHAXgqixHfVlzuBQaPOTac8hn43aPhMNk6rMe3g==", "license": "MIT", "engines": {"node": ">=18.0.0"}}, "node_modules/ipaddr.js": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.2.0.tgz", "integrity": "sha512-Ag3wB2o37wslZS19hZqorUnrnzSkpOVy+IiiDEiTqNubEYpYuHWIf6K4psgN2ZWKExS4xhVCrRVfb/wfW8fWJA==", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/json-server": {"version": "1.0.0-beta.3", "resolved": "https://registry.npmjs.org/json-server/-/json-server-1.0.0-beta.3.tgz", "integrity": "sha512-DwE69Ep5ccwIJZBUIWEENC30Yj8bwr4Ax9W9VoIWAYnB8Sj4ReptscO8/DRHv/nXwVlmb3Bk73Ls86+VZdYkkA==", "license": "SEE LICENSE IN ./LICENSE", "dependencies": {"@tinyhttp/app": "^2.4.0", "@tinyhttp/cors": "^2.0.1", "@tinyhttp/logger": "^2.0.0", "chalk": "^5.3.0", "chokidar": "^4.0.1", "dot-prop": "^9.0.0", "eta": "^3.5.0", "inflection": "^3.0.0", "json5": "^2.2.3", "lowdb": "^7.0.1", "milliparsec": "^4.0.0", "sirv": "^2.0.4", "sort-on": "^6.1.0"}, "bin": {"json-server": "lib/bin.js"}, "engines": {"node": ">=18.3"}}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/lowdb": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/lowdb/-/lowdb-7.0.1.tgz", "integrity": "sha512-neJAj8GwF0e8EpycYIDFqEPcx9Qz4GUho20jWFR7YiFeXzF1YMLdxB36PypcTSPMA+4+LvgyMacYhlr18Zlymw==", "license": "MIT", "dependencies": {"steno": "^4.0.2"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/typicode"}}, "node_modules/milliparsec": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/milliparsec/-/milliparsec-4.0.0.tgz", "integrity": "sha512-/wk9d4Z6/9ZvoEH/6BI4TrTCgmkpZPuSRN/6fI9aUHOfXdNTuj/VhLS7d+NqG26bi6L9YmGXutVYvWC8zQ0qtA==", "license": "MIT", "engines": {"node": ">=20"}}, "node_modules/mime": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/mime/-/mime-4.0.4.tgz", "integrity": "sha512-v8yqInVjhXyqP6+Kw4fV3ZzeMRqEW6FotRsKXjRS5VMTNIuXsdRoAvklpoRgSqXm6o9VNH4/C0mgedko9DdLsQ==", "funding": ["https://github.com/sponsors/broofa"], "license": "MIT", "bin": {"mime": "bin/cli.js"}, "engines": {"node": ">=16"}}, "node_modules/mrmime": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/mrmime/-/mrmime-2.0.1.tgz", "integrity": "sha512-Y3wQdFg2Va6etvQ5I82yUhGdsKrcYox6p7FfL1LbK2J4V01F9TGlepTIhnK24t7koZibmg82KGglhA1XK5IsLQ==", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/negotiator": {"version": "0.6.4", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.4.tgz", "integrity": "sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/readdirp": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-4.1.2.tgz", "integrity": "sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==", "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/regexparam": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/regexparam/-/regexparam-2.0.2.tgz", "integrity": "sha512-A1PeDEYMrkLrfyOwv2jwihXbo9qxdGD3atBYQA9JJgreAx8/7rC6IUkWOw2NQlOxLp2wL0ifQbh1HuidDfYA6w==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/sirv": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/sirv/-/sirv-2.0.4.tgz", "integrity": "sha512-94Bdh3cC2PKrbgSOUqTiGPWVZeSiXfKOVZNJniWoqrWrRkB1CJzBU3NEbiTsPcYy1lDsANA/THzS+9WBiy5nfQ==", "license": "MIT", "dependencies": {"@polka/url": "^1.0.0-next.24", "mrmime": "^2.0.0", "totalist": "^3.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/sort-on": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/sort-on/-/sort-on-6.1.0.tgz", "integrity": "sha512-WTECP0nYNWO1n2g5bpsV0yZN9cBmZsF8ThHFbOqVN0HBFRoaQZLLEMvMmJlKHNPYQeVngeI5+jJzIfFqOIo1OA==", "license": "MIT", "dependencies": {"dot-prop": "^9.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/steno": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/steno/-/steno-4.0.2.tgz", "integrity": "sha512-yhPIQXjrlt1xv7dyPQg2P17URmXbuM5pdGkpiMB3RenprfiBlvK415Lctfe0eshk90oA7/tNq7WEiMK8RSP39A==", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/typicode"}}, "node_modules/totalist": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/totalist/-/totalist-3.0.1.tgz", "integrity": "sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/type-fest": {"version": "4.41.0", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-4.41.0.tgz", "integrity": "sha512-TeTSQ6H5YHvpqVwBRcnLDCBnDOHWYu7IvGbHT6N8AOymcr9PJGjc1GTtiWZTYg0NCgYwvnYWEkVChQAr9bjfwA==", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}}