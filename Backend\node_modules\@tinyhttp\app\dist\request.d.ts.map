{"version": 3, "file": "request.d.ts", "sourceRoot": "", "sources": ["../src/request.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,mBAAmB,EAAE,eAAe,EAAE,MAAM,WAAW,CAAA;AACrE,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAA;AAEtD,OAAO,EAAE,KAAK,KAAK,EAAwC,MAAM,sBAAsB,CAAA;AACvF,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAA;AAE1D,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAA;AAClD,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,UAAU,CAAA;AAGnC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,UAAU,CAAA;AACtC,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,UAAU,CAAA;AACzC,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,eAAe,CAAA;AAE9C,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAA;AAE5C,MAAM,MAAM,IAAI,GAAG;IACjB,QAAQ,EAAE,MAAM,CAAA;IAChB,IAAI,CAAC,EAAE,MAAM,CAAA;CACd,CAAA;AAQD,eAAO,MAAM,WAAW,QAAS,OAAO,SAAS,KAAK,KAAG,QAUxD,CAAA;AAgDD,eAAO,MAAM,OAAO,QAAS,OAAO,SAAS,KAAK,KAAG,IAAI,GAAG,SAY3D,CAAA;AAED,eAAO,MAAM,KAAK,QAAS,IAAI,CAAC,OAAO,EAAE,SAAS,GAAG,YAAY,GAAG,QAAQ,CAAC,SAAS,KAAK,KAAG,MAAM,GAAG,SAC5D,CAAA;AAE3C,eAAO,MAAM,MAAM,QAAS,IAAI,CAAC,OAAO,EAAE,SAAS,GAAG,YAAY,GAAG,QAAQ,CAAC,SAAS,KAAK,KAAG,MAAM,EAAE,GAAG,SACzF,CAAA;AAEjB,eAAO,MAAM,aAAa,QAAS,OAAO,SAAS,KAAK,+BAAwB,MAAM,EAOrF,CAAA;AAED,MAAM,MAAM,UAAU,GAAG,eAAe,CAAC,QAAQ,CAAC,GAAG;IACnD,SAAS,EAAE,OAAO,CAAA;CACnB,CAAA;AAED,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,CAAA;AAEhD,YAAY,EAAE,SAAS,EAAE,CAAA;AAEzB,KAAK,cAAc,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,EAAE,CAAA;AAEjD,MAAM,WAAW,OAAQ,SAAQ,eAAe;IAC9C,WAAW,EAAE,MAAM,CAAA;IACnB,IAAI,EAAE,MAAM,CAAA;IACZ,GAAG,EAAE,MAAM,CAAA;IACX,OAAO,EAAE,MAAM,CAAA;IACf,KAAK,EAAE,cAAc,CAAA;IACrB,MAAM,EAAE,SAAS,CAAA;IACjB,UAAU,EAAE,UAAU,CAAA;IACtB,MAAM,EAAE,SAAS,GAAG,MAAM,CAAA;IAC1B,KAAK,CAAC,EAAE,UAAU,CAAA;IAClB,QAAQ,EAAE,QAAQ,CAAA;IAClB,MAAM,EAAE,OAAO,CAAA;IACf,GAAG,EAAE,OAAO,CAAA;IACZ,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,EAAE,CAAC,EAAE,MAAM,CAAA;IACX,GAAG,CAAC,EAAE,MAAM,EAAE,CAAA;IACd,UAAU,CAAC,EAAE,MAAM,EAAE,CAAA;IACrB,GAAG,EAAE,CAAC,UAAU,SAAS,MAAM,EAAE,MAAM,EAAE,UAAU,KAAK,mBAAmB,CAAC,UAAU,CAAC,CAAA;IACvF,KAAK,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,SAAS,CAAA;IAC7E,OAAO,EAAE,CAAC,GAAG,KAAK,EAAE,MAAM,EAAE,KAAK,cAAc,CAAA;IAC/C,gBAAgB,EAAE,CAAC,GAAG,SAAS,EAAE,MAAM,EAAE,KAAK,cAAc,CAAA;IAC5D,eAAe,EAAE,CAAC,GAAG,QAAQ,EAAE,MAAM,EAAE,KAAK,cAAc,CAAA;IAC1D,gBAAgB,EAAE,CAAC,GAAG,SAAS,EAAE,MAAM,EAAE,KAAK,cAAc,CAAA;IAC5D,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,MAAM,EAAE,KAAK,MAAM,GAAG,OAAO,CAAA;IAC5C,OAAO,CAAC,EAAE,GAAG,CAAA;IACb,aAAa,CAAC,EAAE,GAAG,CAAA;IACnB,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAA;IAC1B,KAAK,CAAC,EAAE,OAAO,CAAA;IACf,KAAK,CAAC,EAAE,OAAO,CAAA;IACf,IAAI,CAAC,EAAE,GAAG,CAAA;IACV,GAAG,CAAC,EAAE,GAAG,CAAA;CACV"}