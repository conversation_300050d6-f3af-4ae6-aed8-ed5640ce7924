import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { NavLink, useNavigate } from "react-router-dom";
import { logoutUser } from "../store/Actions/UserAction";

const NavBar = () => {
  const user = useSelector((state) => state.user.user);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const logOutHandler = () => {
    dispatch(logoutUser());
    navigate("/login");
  };
  return (
    <nav className="flex justify-center items-center gap-10 h-14 bg-gray-900 text-gray-400">
      <NavLink className={(e)=>(e.isActive ? "text-white" : "text-gray-400")} to="/">Home</NavLink>
      <NavLink className={(e)=>(e.isActive ? "text-white" : "text-gray-400")} to="/products">Products</NavLink>
      {user?.isAdmin && (
        <NavLink className={(e)=>(e.isActive ? "text-white" : "text-gray-400")} to="/admin/create-product">Add Product</NavLink>
      )}
      <div>        
      {user ? (
        <button onClick={logOutHandler} className="px-5 py-2 bg-red-400 rounded text-white cursor-pointer" to="/logout">Logout</button>
      ) : (
      <NavLink className={(e)=>(e.isActive ? "text-white" : "text-gray-400")} to="/login">Login</NavLink>
      )}
      </div>
    </nav>
  );
};

export default NavBar;
