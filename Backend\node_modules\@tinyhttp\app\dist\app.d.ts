import { type Server } from 'node:http';
import type { Handler, Middleware, NextFunction, UseMethodParams } from '@tinyhttp/router';
import { Router } from '@tinyhttp/router';
import type { TemplateEngineOptions } from './index.js';
import type { <PERSON>rror<PERSON>andler } from './onError.js';
import type { Request } from './request.js';
import type { Response } from './response.js';
import type { AppConstructor, AppInterface, AppRenderOptions, AppSettings, TemplateEngine } from './types.js';
/**
 * `App` class - the starting point of tinyhttp app.
 *
 * With the `App` you can:
 * * use routing methods and `.use(...)`
 * * set no match (404) and error (500) handlers
 * * configure template engines
 * * store data in locals
 * * listen the http server on a specified port
 *
 * In case you use TypeScript, you can pass custom types to this class because it is also a generic class.
 *
 * Example:
 *
 * ```ts
 * interface CoolReq extends Request {
 *  genericsAreDope: boolean
 * }
 *
 * const app = App<any, CoolReq, Response>()
 * ```
 */
export declare class App<Req extends Request = Request, Res extends Response = Response> extends Router<App, Req, Res> implements AppInterface<Req, Res> {
    #private;
    middleware: Middleware<Req, Res>[];
    locals: Record<string, unknown>;
    noMatchHandler: Handler;
    onError: ErrorHandler;
    settings: AppSettings;
    engines: Record<string, TemplateEngine>;
    applyExtensions?: Handler;
    attach: (req: Req, res: Res, next?: NextFunction) => void;
    cache: Record<string, unknown>;
    constructor(options?: AppConstructor<Req, Res>);
    set<K extends keyof AppSettings>(setting: K, value: AppSettings[K]): this;
    enable<K extends keyof AppSettings>(setting: K): this;
    enabled<K extends keyof AppSettings>(setting: K): boolean;
    disable<K extends keyof AppSettings>(setting: K): this;
    path(): any;
    engine<RenderOptions extends TemplateEngineOptions = TemplateEngineOptions>(ext: string, fn: TemplateEngine<RenderOptions>): this;
    render<RenderOptions extends TemplateEngineOptions = TemplateEngineOptions>(name: string, data?: Record<string, unknown>, options?: AppRenderOptions<RenderOptions>, cb?: (err: unknown, html?: unknown) => void): void;
    use(...args: UseMethodParams<Req, Res, AppInterface<any, any>>): this;
    route(path: string): App<any, any>;
    handler<RenderOptions extends TemplateEngineOptions = TemplateEngineOptions>(req: Req, res: Res, next?: NextFunction): void;
    listen(port?: number, cb?: () => void, host?: string): Server<typeof import("http").IncomingMessage, typeof import("http").ServerResponse>;
}
//# sourceMappingURL=app.d.ts.map