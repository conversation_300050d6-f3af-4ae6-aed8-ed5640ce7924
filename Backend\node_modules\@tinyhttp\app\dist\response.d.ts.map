{"version": 3, "file": "response.d.ts", "sourceRoot": "", "sources": ["../src/response.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,mBAAmB,EAAE,cAAc,EAAE,MAAM,WAAW,CAAA;AACpE,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAA;AACxD,OAAO,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,eAAe,CAAA;AAClF,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,UAAU,CAAA;AACnC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,cAAc,CAAA;AAC3C,OAAO,KAAK,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,MAAM,YAAY,CAAA;AAEzE,eAAO,MAAM,cAAc,GACxB,CAAC,SAAS,qBAAqB,gCAAgC,OAAO,OAAO,QAAQ,OAAO,GAAG,YACzF,MAAM,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,YAAY,gBAAgB,CAAC,CAAC,CAAC,KAAG,QAM9E,CAAA;AAEH,MAAM,WAAW,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAE,SAAQ,cAAc;IAC3D,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAClF,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC/E,GAAG,CAAC,UAAU,SAAS,MAAM,EAAE,KAAK,EAAE,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAA;IAClF,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC1B,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,eAAe,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC5F,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC1B,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IACnC,UAAU,CAAC,UAAU,EAAE,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC3C,MAAM,CACJ,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACvC,OAAO,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC;QAAE,MAAM,EAAE,OAAO,CAAA;KAAE,CAAC,GACxD,QAAQ,CAAC,CAAC,CAAC,CAAA;IACd,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,gBAAgB,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAClE,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAClC,KAAK,CAAC,KAAK,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IACpD,MAAM,CAAC,CAAC,SAAS,qBAAqB,GAAG,qBAAqB,EAC5D,IAAI,EAAE,MAAM,EACZ,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAC1B,OAAO,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAC5B,QAAQ,CAAC,CAAC,CAAC,CAAA;IACd,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAChC,MAAM,CAAC,GAAG,EAAE,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IACrC,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IACnD,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC/B,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,eAAe,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC9G,UAAU,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC1C,GAAG,CAAC,EAAE,GAAG,CAAA;IACT,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;IAC3B;;;;;;OAMG;IACH,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAE5B,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;CAC/C"}