{"name": "json-server", "version": "1.0.0-beta.3", "description": "", "type": "module", "bin": {"json-server": "lib/bin.js"}, "types": "lib", "files": ["lib", "views"], "engines": {"node": ">=18.3"}, "scripts": {"dev": "tsx watch src/bin.ts fixtures/db.json", "build": "rm -rf lib && tsc", "test": "node --import tsx/esm --test src/*.test.ts", "lint": "eslint src", "prepare": "husky", "prepublishOnly": "npm run build"}, "keywords": [], "author": "typicode <<EMAIL>>", "license": "SEE LICENSE IN ./LICENSE", "repository": {"type": "git", "url": "git+https://github.com/typicode/json-server.git"}, "devDependencies": {"@eslint/js": "^9.11.0", "@sindresorhus/tsconfig": "^6.0.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "concurrently": "^9.0.1", "eslint": "^9.11.0", "get-port": "^7.1.0", "globals": "^15.9.0", "husky": "^9.1.6", "tempy": "^3.1.0", "tsx": "^4.19.1", "type-fest": "^4.26.1", "typescript": "^5.6.2", "typescript-eslint": "^8.6.0"}, "dependencies": {"@tinyhttp/app": "^2.4.0", "@tinyhttp/cors": "^2.0.1", "@tinyhttp/logger": "^2.0.0", "chalk": "^5.3.0", "chokidar": "^4.0.1", "dot-prop": "^9.0.0", "eta": "^3.5.0", "inflection": "^3.0.0", "json5": "^2.2.3", "lowdb": "^7.0.1", "milliparsec": "^4.0.0", "sirv": "^2.0.4", "sort-on": "^6.1.0"}}