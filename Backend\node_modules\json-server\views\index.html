<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    html {
      font-size: 16px;
      line-height: 1.5;
      background-color: #fff;
      color: #000;
    }

    body {
      margin: 0 auto;
      max-width: 720px;
      padding: 0 16px;
      font-family: sans-serif;
    }

    a {
      color: #db2777;
      text-decoration: none;
    }

    header {
      margin-bottom: 32px;
      padding: 16px 0;
    }

    nav {
      display: flex;
      justify-content: space-between;
    }

    nav div a {
      margin-left: 16px;
    }

    ul {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    li {
      margin-bottom: 8px;
    }

    /* Dark mode styles */
    @media (prefers-color-scheme: dark) {
      html {
        background-color: #1e293b;
        color: #fff;
      }

      a {

      }
    }

  </style>
</head>

<body>
  <header>
    <nav>
      <strong>JSON Server</strong>
      <div>
        <a href="https://github.com/typicode/json-server">Docs</a>
        <a href="https://github.com/sponsors/typicode">♡ Sponsor</a>
      </div>
    </nav>
  </header>
  <main class="my-12">
    <p class="bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 text-transparent bg-clip-text">✧*｡٩(ˊᗜˋ*)و✧*｡</p>
    <% if (Object.keys(it.data).length===0) { %>
      <p>No resources found in JSON file</p>
    <% } %>
    <% Object.entries(it.data).forEach(function([name]) { %>
      <ul>
        <li>
          <a href="<%= name %>">/<%= name %></a>
          <span>
            <% if (Array.isArray(it.data[name])) { %>
              - <%= it.data[name].length %>
                <%= it.data[name].length> 1 ? 'items' : 'item' %>
          </span>
          <% } %>
        </li>
      </ul>
    <% }) %>
  </main>
</body>

</html>