{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["const pairSplitRegExp = /; */\n\n/**\n * RegExp to match field-content in RFC 7230 sec 3.2\n *\n * field-content = field-vchar [ 1*( SP / HTAB ) field-vchar ]\n * field-vchar   = VCHAR / obs-text\n * obs-text      = %x80-FF\n */\n\n// biome-ignore lint/suspicious/noControlCharactersInRegex: <explanation>\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/\n\nfunction tryDecode(str: string, decode: (str: string) => string) {\n  try {\n    return decode(str)\n  } catch (e) {\n    return str\n  }\n}\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n *\n */\nexport function parse(\n  str: string,\n  options: {\n    decode: (str: string) => string\n  } = {\n    decode: decodeURIComponent\n  }\n): Record<string, string> {\n  const obj: Record<string, string> = {}\n  const pairs = str.split(pairSplitRegExp)\n\n  for (const pair of pairs) {\n    let eqIdx = pair.indexOf('=')\n\n    // skip things that don't look like key=value\n    if (eqIdx < 0) continue\n\n    const key = pair.slice(0, eqIdx).trim()\n    let val = pair.slice(++eqIdx, pair.length).trim()\n\n    // quoted values\n    if ('\"' === val[0]) val = val.slice(1, -1)\n\n    // only assign once\n    if (obj[key] == null) obj[key] = tryDecode(val, options.decode)\n  }\n\n  return obj\n}\n\nexport type SerializeOptions = Partial<{\n  encode: (str: string) => string\n  maxAge: number\n  domain: string\n  path: string\n  httpOnly: boolean\n  secure: boolean\n  sameSite: boolean | 'Strict' | 'strict' | 'Lax' | 'lax' | 'None' | 'none' | string\n  expires: Date\n}>\n\nexport function serialize(name: string, val: string, opt: SerializeOptions = {}): string {\n  if (!opt.encode) opt.encode = encodeURIComponent\n\n  if (!fieldContentRegExp.test(name)) throw new TypeError('argument name is invalid')\n\n  const value = opt.encode(val)\n\n  if (value && !fieldContentRegExp.test(value)) throw new TypeError('argument val is invalid')\n\n  let str = `${name}=${value}`\n\n  if (null != opt.maxAge) {\n    const maxAge = opt.maxAge - 0\n\n    if (Number.isNaN(maxAge) || !Number.isFinite(maxAge)) throw new TypeError('option maxAge is invalid')\n\n    str += `; Max-Age=${Math.floor(maxAge)}`\n  }\n\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) throw new TypeError('option domain is invalid')\n\n    str += `; Domain=${opt.domain}`\n  }\n\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) throw new TypeError('option path is invalid')\n\n    str += `; Path=${opt.path}`\n  }\n\n  if (opt.expires) str += `; Expires=${opt.expires.toUTCString()}`\n\n  if (opt.httpOnly) str += '; HttpOnly'\n\n  if (opt.secure) str += '; Secure'\n\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite\n\n    switch (sameSite) {\n      case true:\n      case 'strict':\n        str += '; SameSite=Strict'\n        break\n      case 'lax':\n        str += '; SameSite=Lax'\n        break\n      case 'none':\n        str += '; SameSite=None'\n        break\n      default:\n        throw new TypeError('option sameSite is invalid')\n    }\n  }\n\n  return str\n}\n"], "names": [], "mappings": "AAAA,MAAM,kBAAkB;AAWxB,MAAM,qBAAqB;AAE3B,SAAS,UAAU,KAAa,QAAiC;AAC3D,MAAA;AACF,WAAO,OAAO,GAAG;AAAA,WACV,GAAG;AACH,WAAA;AAAA,EACT;AACF;AASgB,SAAA,MACd,KACA,UAEI;AAAA,EACF,QAAQ;AACV,GACwB;AACxB,QAAM,MAA8B,CAAA;AAC9B,QAAA,QAAQ,IAAI,MAAM,eAAe;AAEvC,aAAW,QAAQ,OAAO;AACpB,QAAA,QAAQ,KAAK,QAAQ,GAAG;AAG5B,QAAI,QAAQ,EAAG;AAEf,UAAM,MAAM,KAAK,MAAM,GAAG,KAAK,EAAE;AAC7B,QAAA,MAAM,KAAK,MAAM,EAAE,OAAO,KAAK,MAAM,EAAE;AAGvC,QAAA,QAAQ,IAAI,CAAC,SAAS,IAAI,MAAM,GAAG,EAAE;AAGrC,QAAA,IAAI,GAAG,KAAK,KAAM,KAAI,GAAG,IAAI,UAAU,KAAK,QAAQ,MAAM;AAAA,EAChE;AAEO,SAAA;AACT;AAaO,SAAS,UAAU,MAAc,KAAa,MAAwB,CAAA,GAAY;AACvF,MAAI,CAAC,IAAI,OAAQ,KAAI,SAAS;AAE1B,MAAA,CAAC,mBAAmB,KAAK,IAAI,EAAS,OAAA,IAAI,UAAU,0BAA0B;AAE5E,QAAA,QAAQ,IAAI,OAAO,GAAG;AAExB,MAAA,SAAS,CAAC,mBAAmB,KAAK,KAAK,EAAG,OAAM,IAAI,UAAU,yBAAyB;AAE3F,MAAI,MAAM,GAAG,IAAI,IAAI,KAAK;AAEtB,MAAA,QAAQ,IAAI,QAAQ;AAChB,UAAA,SAAS,IAAI,SAAS;AAE5B,QAAI,OAAO,MAAM,MAAM,KAAK,CAAC,OAAO,SAAS,MAAM,EAAG,OAAM,IAAI,UAAU,0BAA0B;AAEpG,WAAO,aAAa,KAAK,MAAM,MAAM,CAAC;AAAA,EACxC;AAEA,MAAI,IAAI,QAAQ;AACV,QAAA,CAAC,mBAAmB,KAAK,IAAI,MAAM,EAAG,OAAM,IAAI,UAAU,0BAA0B;AAEjF,WAAA,YAAY,IAAI,MAAM;AAAA,EAC/B;AAEA,MAAI,IAAI,MAAM;AACR,QAAA,CAAC,mBAAmB,KAAK,IAAI,IAAI,EAAG,OAAM,IAAI,UAAU,wBAAwB;AAE7E,WAAA,UAAU,IAAI,IAAI;AAAA,EAC3B;AAEA,MAAI,IAAI,QAAS,QAAO,aAAa,IAAI,QAAQ,YAAa,CAAA;AAE1D,MAAA,IAAI,SAAiB,QAAA;AAErB,MAAA,IAAI,OAAe,QAAA;AAEvB,MAAI,IAAI,UAAU;AACV,UAAA,WAAW,OAAO,IAAI,aAAa,WAAW,IAAI,SAAS,YAAY,IAAI,IAAI;AAErF,YAAQ,UAAU;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AACI,eAAA;AACP;AAAA,MACF,KAAK;AACI,eAAA;AACP;AAAA,MACF,KAAK;AACI,eAAA;AACP;AAAA,MACF;AACQ,cAAA,IAAI,UAAU,4BAA4B;AAAA,IACpD;AAAA,EACF;AAEO,SAAA;AACT;"}