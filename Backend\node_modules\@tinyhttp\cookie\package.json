{"name": "@tinyhttp/cookie", "version": "2.1.1", "type": "module", "description": "HTTP cookie parser and serializer for Node.js", "homepage": "https://github.com/tinyhttp/tinyhttp/tree/master/packages/cookie#readme", "engines": {"node": ">=12.20.0"}, "funding": {"type": "individual", "url": "https://github.com/tinyhttp/tinyhttp?sponsor=1"}, "repository": {"type": "git", "url": "https://github.com/tinyhttp/tinyhttp.git", "directory": "packages/cookie"}, "types": "./dist/index.d.ts", "exports": "./dist/index.js", "keywords": ["tinyhttp", "node.js", "web framework", "web", "backend", "cookie"], "files": ["dist"], "author": "v1rtl", "license": "MIT", "dependencies": {}, "scripts": {"dev": "vite", "build": "vite build"}}