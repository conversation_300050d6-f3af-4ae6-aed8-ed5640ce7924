{"name": "@tinyhttp/content-disposition", "description": "content-disposition rewrite in TypeScript", "version": "2.2.2", "license": "MIT", "homepage": "https://tinyhttp.v1rtl.site", "funding": {"type": "individual", "url": "https://github.com/tinyhttp/tinyhttp?sponsor=1"}, "repository": {"type": "git", "url": "https://github.com/tinyhttp/tinyhttp.git", "directory": "packages/content-disposition"}, "engines": {"node": ">=12.20.0"}, "type": "module", "types": "./dist/index.d.ts", "exports": "./dist/index.js", "files": ["dist"], "scripts": {"build": "tsc"}}