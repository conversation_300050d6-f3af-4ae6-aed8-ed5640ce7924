{"version": 3, "file": "onError.js", "sourceRoot": "", "sources": ["../src/onError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,WAAW,CAAA;AAQxC,MAAM,CAAC,MAAM,cAAc,GAAiB,UAAqB,GAAQ,EAAE,IAAa,EAAE,GAAa;IACrG,IAAI,IAAI,CAAC,OAAO,KAAK,cAAc,IAAI,IAAI,CAAC,MAAM;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAA;IAE9F,IAAI,GAAG,YAAY,KAAK;QAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAE5C,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAA;IAE7D,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;QAAE,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;SAC3E,IAAI,IAAI,IAAI,YAAY;QAAE,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAA;;QACrE,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;AAC1C,CAAC,CAAA"}