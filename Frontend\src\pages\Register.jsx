import React from "react";
import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { registerUser } from "../store/Actions/UserAction";
import { nanoid } from "@reduxjs/toolkit";

const Register = () => {
    const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const registerHandler = (data) => {
    data.id = nanoid();
    data.isAdmin = false; 
    dispatch(registerUser(data));     //dispatching the user data to the store
    reset();
    navigate("/login");
  };
  return (
    <>Register Page<br/>
        <form onSubmit={handleSubmit(registerHandler)}>
          <input
            {...register("username", { required: "This field is required." })}
            type="text"
            name="username"
            placeholder="Enter the Username"
          />
          {errors.username && <p>{errors.username.message}</p>}
          <br />
          <input
            {...register("email", { required: "This field is required." })}
            type="text"
            name="email"
            placeholder="Enter the Email"
          />
          {errors.email && <p>{errors.email.message}</p>}
          <br />
          <input
            {...register("password", { required: "This field is required." })}
            type="password"
            name="password"
            placeholder="Enter the Password"
          />
          {errors.password && <p>{errors.password.message}</p>}
          <br />
          <button className="bg-blue-500 text-white px-4 py-2 rounded" type="submit">Register</button>
          
        </form>
        <p>Already have an account? <Link className="text-blue-500" to="/login">Login</Link></p>
        </>
  )
}

export default Register
