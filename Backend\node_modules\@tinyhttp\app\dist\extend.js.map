{"version": 3, "file": "extend.js", "sourceRoot": "", "sources": ["../src/extend.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAA;AAC9C,OAAO,EACL,qBAAqB,EACrB,UAAU,EACV,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,EACf,cAAc,EACd,kBAAkB,EAClB,gBAAgB,EAChB,KAAK,EACN,MAAM,eAAe,CAAA;AACtB,OAAO,EACL,MAAM,EACN,UAAU,EACV,WAAW,EACX,QAAQ,EACR,cAAc,EACd,iBAAiB,EACjB,IAAI,EACJ,QAAQ,EACR,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,cAAc,EACd,SAAS,EACT,SAAS,EACT,cAAc,EACd,iBAAiB,EACjB,aAAa,EACb,MAAM,EACP,MAAM,eAAe,CAAA;AAItB,OAAO,EAAgB,aAAa,EAAE,MAAM,cAAc,CAAA;AAC1D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,cAAc,CAAA;AAElE,OAAO,EAAE,cAAc,EAAE,MAAM,eAAe,CAAA;AAG9C;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAsE,GAAQ,EAAE,EAAE,CAChH,CAAC,CAAC,GAAY,EAAE,GAA4B,EAAE,IAAkB,EAAE,EAAE;IAClE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAA;IAExB,GAAG,CAAC,GAAG,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAA;IAChC,GAAG,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAA;IAE/B,IAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,eAAe,EAAE,CAAC;QAC9B,GAAG,CAAC,GAAG,GAAG,GAAG,CAAA;QACb,GAAG,CAAC,GAAG,GAAG,GAAG,CAAA;IACf,CAAC;IAED,IAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,iBAAiB,EAAE,CAAC;QAChC,IAAI,KAAK,GAAG,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAG,aAAa,CAAC,KAAI,CAAC,CAAA;QAC1C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YACzC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;YACtB,QAAQ,CAAC,aAAa,CAAC,GAAG,KAAK,CAAA;QACjC,CAAC;QACD,GAAG,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QACtC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,QAAQ,KAAK,OAAO,CAAA;QACrC,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QAChC,GAAG,CAAC,QAAQ,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,CAAA;QAC7B,GAAG,CAAC,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAA;QACrB,GAAG,CAAC,UAAU,GAAG,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,eAAe,CAAC,CAAA;QACpE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QAC1B,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IAC9B,CAAC;IAED,GAAG,CAAC,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IAEnC,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;IACnB,GAAG,CAAC,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAA;IACnC,GAAG,CAAC,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;IAC7B,GAAG,CAAC,eAAe,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAA;IAC7C,GAAG,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAA;IAC/C,GAAG,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAA;IAE/C,GAAG,CAAC,GAAG,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAA;IAEpC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,SAAS,CAAW,GAAG,CAAC,CAAA;IAC/C,GAAG,CAAC,IAAI,GAAG,IAAI,CAAoB,GAAG,EAAE,GAAG,CAAC,CAAA;IAC5C,GAAG,CAAC,IAAI,GAAG,IAAI,CAAW,GAAG,CAAC,CAAA;IAC9B,GAAG,CAAC,MAAM,GAAG,MAAM,CAAW,GAAG,CAAC,CAAA;IAClC,GAAG,CAAC,UAAU,GAAG,UAAU,CAAoB,GAAG,EAAE,GAAG,CAAC,CAAA;IACxD,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAoB,GAAG,EAAE,GAAG,CAAC,CAAA;IACpD,GAAG,CAAC,IAAI,GAAG,cAAc,CAAW,GAAG,CAAC,CAAA;IACxC,GAAG,CAAC,QAAQ,GAAG,iBAAiB,CAAoB,GAAG,EAAE,GAAG,CAAC,CAAA;IAC7D,GAAG,CAAC,KAAK,GAAG,cAAc,CAAW,GAAG,CAAC,CAAA;IACzC,GAAG,CAAC,IAAI,GAAG,aAAa,CAAW,GAAG,CAAC,CAAA;IACvC,GAAG,CAAC,MAAM,GAAG,SAAS,CAAoB,GAAG,EAAE,GAAG,CAAC,CAAA;IACnD,GAAG,CAAC,WAAW,GAAG,WAAW,CAAoB,GAAG,EAAE,GAAG,CAAC,CAAA;IAC1D,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;IAC1C,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IAC3C,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IACvC,GAAG,CAAC,UAAU,GAAG,UAAU,CAAW,GAAG,CAAC,CAAA;IAC1C,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAoB,GAAG,EAAE,GAAG,CAAC,CAAA;IACpD,GAAG,CAAC,MAAM,GAAG,MAAM,CAAW,GAAG,CAAC,CAAA;IAClC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAE9C,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAA;IACtG,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAA;IAEtB,IAAI,EAAE,CAAA;AACR,CAAC,CAAY,CAAA"}