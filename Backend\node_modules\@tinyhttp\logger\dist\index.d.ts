import { type IncomingMessage as Request, type ServerResponse as Response } from 'node:http';
export declare enum LogLevel {
    error = "error",
    warn = "warn",
    trace = "trace",
    info = "info",
    log = "log"
}
export type LoggerOptions = Partial<{
    methods: string[];
    output: {
        color: boolean;
        filename?: string;
        callback: (string: string) => void;
        level?: LogLevel;
    };
    timestamp: boolean | {
        format?: string;
    };
    emoji: boolean;
    ip: boolean;
    ignore: string[];
}>;
export declare const logger: (options?: LoggerOptions) => (req: Request, res: Response, next?: () => void) => void;
